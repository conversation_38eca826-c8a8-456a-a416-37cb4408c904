import { useState, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import DemoLayout from "@/components/layout/DemoLayout";
import { useDemo } from "@/context/DemoContext";
import { PlayerAvatar } from "@/components/players/PlayerAvatar";
import { EmptyState } from "@/components/ui/empty-state";
import { MobileTable } from "@/components/ui/mobile-table";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useToast } from "@/hooks/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Plus, Users } from "lucide-react";
import { Badge } from "@/components/ui/badge";

// Enhanced components
import { EnhancedCard } from "@/components/ui/enhanced-card";
import { ResponsiveTable } from "@/components/ui/responsive-table";
import { AnimatedButton } from "@/components/ui/animated-button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>L<PERSON>, TabsTrigger } from "@/components/ui/tabs";

const DemoPlayersPage = () => {
  const { t } = useTranslation();
  const { players, matches } = useDemo();
  const [loading, setLoading] = useState(false);
  const isMobile = useMediaQuery("(max-width: 768px)");
  const { toast } = useToast();

  // Set page title
  useEffect(() => {
    document.title = `${t('nav.players')} - ${t('app.name')} Demo`;
  }, [t]);

  // Calculate player statistics
  const playersWithStats = useMemo(() => {
    return players.map(player => {
      const playerMatches = matches.filter(match => 
        match.teama.includes(player.id) || match.teamb.includes(player.id)
      );

      let wins = 0;
      let draws = 0;
      let losses = 0;
      let goals = 0;

      playerMatches.forEach(match => {
        const isTeamA = match.teama.includes(player.id);
        
        // Count goals
        if (match.goalscorers) {
          goals += match.goalscorers.filter(g => g.playerId === player.id).length;
        }
        
        // Count wins/draws/losses
        if (match.winner === 'Draw') {
          draws++;
        } else if (
          (isTeamA && match.winner === 'A') || 
          (!isTeamA && match.winner === 'B')
        ) {
          wins++;
        } else {
          losses++;
        }
      });

      const played = playerMatches.length;
      const winRate = played > 0 ? (wins / played) * 100 : 0;
      const avgRating = Math.round((player.skills + player.effort + player.stamina) / 3);

      return {
        ...player,
        played,
        wins,
        draws,
        losses,
        goals,
        winRate,
        avgRating,
      };
    });
  }, [players, matches]);

  const handleAddPlayer = () => {
    toast({
      title: t('demo.readOnlyMode'),
      description: t('demo.addPlayerNotAvailable', 'Adding players is not available in demo mode. Sign up to manage your own players!'),
      variant: 'default',
    });
  };

  const handleEditPlayer = () => {
    toast({
      title: t('demo.readOnlyMode'),
      description: t('demo.editPlayerNotAvailable', 'Editing players is not available in demo mode. Sign up to manage your own players!'),
      variant: 'default',
    });
  };

  const handleDeletePlayer = () => {
    toast({
      title: t('demo.readOnlyMode'),
      description: t('demo.deletePlayerNotAvailable', 'Deleting players is not available in demo mode. Sign up to manage your own players!'),
      variant: 'default',
    });
  };

  // Desktop table columns
  const columns = [
    {
      header: t('players.name'),
      accessorKey: 'name',
      cell: (player: any) => (
        <div className="flex items-center gap-3">
          <PlayerAvatar
            playerId={player.id}
            playerName={player.name}
            avatarUrl={player.avatar_url}
            size="sm"
            editable={false}
          />
          <span className="font-medium">{player.name}</span>
        </div>
      ),
    },
    {
      header: t('players.skills'),
      accessorKey: 'skills',
      cell: (player: any) => (
        <Badge variant="outline" className="font-mono">
          {player.skills}
        </Badge>
      ),
    },
    {
      header: t('players.effort'),
      accessorKey: 'effort',
      cell: (player: any) => (
        <Badge variant="outline" className="font-mono">
          {player.effort}
        </Badge>
      ),
    },
    {
      header: t('players.stamina'),
      accessorKey: 'stamina',
      cell: (player: any) => (
        <Badge variant="outline" className="font-mono">
          {player.stamina}
        </Badge>
      ),
    },
    {
      header: t('players.avgRating'),
      accessorKey: 'avgRating',
      cell: (player: any) => (
        <Badge variant="secondary" className="font-mono">
          {player.avgRating}
        </Badge>
      ),
    },
    {
      header: t('stats.played'),
      accessorKey: 'played',
      cell: (player: any) => (
        <span className="font-medium">{player.played}</span>
      ),
    },
    {
      header: t('stats.winRate'),
      accessorKey: 'winRate',
      cell: (player: any) => (
        <span className="font-medium">{player.winRate.toFixed(1)}%</span>
      ),
    },
    {
      header: t('stats.goals'),
      accessorKey: 'goals',
      cell: (player: any) => (
        <span className="font-medium">{player.goals}</span>
      ),
    },
  ];

  return (
    <DemoLayout title={t('nav.players')}>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-foreground bg-gradient-to-r from-soccer-primary to-soccer-primary-light bg-clip-text text-transparent hover:scale-105 transition-transform duration-300">
              {t('players.title')}
            </h1>
            <p className="text-muted-foreground mt-2">{t('players.subtitle', 'Manage your team roster and player statistics')}</p>
          </div>
          <div>
            <AnimatedButton
              onClick={handleAddPlayer}
              className="bg-soccer-primary hover:bg-soccer-primary/90"
              disabled={loading}
            >
              <Plus className="mr-2 h-4 w-4" /> {t('players.addPlayer')}
            </AnimatedButton>
          </div>
        </div>

        {playersWithStats.length === 0 ? (
          <EmptyState
            icon={Users}
            title={t('players.noPlayers')}
            description={t('players.noPlayersDesc')}
            action={
              <Button onClick={handleAddPlayer} className="bg-soccer-primary hover:bg-soccer-primary/90">
                <Plus className="mr-2 h-4 w-4" />
                {t('players.addFirstPlayer')}
              </Button>
            }
          />
        ) : (
          <>
            {isMobile ? (
              <MobileTable
                data={playersWithStats}
                columns={[
                  {
                    header: t('players.name'),
                    accessorKey: 'name' as keyof typeof playersWithStats[0],
                    priority: "high",
                    cell: (player: any) => (
                      <div className="flex items-center gap-2">
                        <PlayerAvatar
                          playerId={player.id}
                          playerName={player.name}
                          avatarUrl={player.avatar_url}
                          size="xs"
                          editable={false}
                        />
                        <span className="font-medium">{player.name}</span>
                      </div>
                    ),
                  },
                  {
                    header: t('players.avgRating'),
                    accessorKey: 'avgRating' as keyof typeof playersWithStats[0],
                    priority: "medium",
                    cell: (player: any) => (
                      <Badge variant="secondary" className="font-mono">
                        {player.avgRating}
                      </Badge>
                    ),
                  },
                  {
                    header: t('stats.played'),
                    accessorKey: 'played' as keyof typeof playersWithStats[0],
                    priority: "medium",
                    cell: (player: any) => (
                      <span className="font-medium">{player.played}</span>
                    ),
                  },
                  {
                    header: t('stats.winRate'),
                    accessorKey: 'winRate' as keyof typeof playersWithStats[0],
                    priority: "high",
                    cell: (player: any) => (
                      <span className="font-medium">{player.winRate.toFixed(1)}%</span>
                    ),
                  },
                ]}
                onRowClick={handleEditPlayer}
                emptyMessage={t('players.noPlayers')}
              />
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      {columns.map((column, index) => (
                        <TableHead key={index}>{column.header}</TableHead>
                      ))}
                      <TableHead className="w-[100px]">{t('common.actions')}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {playersWithStats.map((player) => (
                      <TableRow key={player.id} className="cursor-pointer hover:bg-muted/50">
                        {columns.map((column, index) => (
                          <TableCell key={index}>
                            {column.cell(player)}
                          </TableCell>
                        ))}
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEditPlayer();
                              }}
                            >
                              {t('common.edit')}
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeletePlayer();
                              }}
                              className="text-red-600 hover:text-red-700"
                            >
                              {t('common.delete')}
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </>
        )}
      </div>
    </DemoLayout>
  );
};

export default DemoPlayersPage;
